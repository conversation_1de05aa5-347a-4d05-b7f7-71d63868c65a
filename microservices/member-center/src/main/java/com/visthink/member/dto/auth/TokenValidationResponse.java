package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * 令牌验证响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "令牌验证响应")
public class TokenValidationResponse {

    @Schema(description = "令牌是否有效", example = "true")
    private Boolean valid;

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "testuser001")
    private String username;

    @Schema(description = "过期时间")
    private LocalDateTime expiresAt;

    @Schema(description = "错误消息")
    private String errorMessage;
}
