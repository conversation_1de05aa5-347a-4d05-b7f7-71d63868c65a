package com.visthink.member.service;

import com.visthink.member.entity.UserAccount;
import com.visthink.common.dto.ApiResponse;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;

/**
 * 用户服务接口
 * 提供用户管理的核心业务逻辑
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface UserService {

    /**
     * 创建用户
     *
     * @param userAccount 用户信息
     * @return 创建的用户信息
     */
    Uni<ApiResponse<UserAccount>> createUser(UserAccount userAccount);

    /**
     * 根据ID获取用户信息
     *
     * @param id 用户ID
     * @return 用户信息
     */
    Uni<ApiResponse<UserAccount>> getUserById(Long id);

    /**
     * 根据用户名获取用户信息
     *
     * @param username 用户名
     * @return 用户信息
     */
    Uni<ApiResponse<UserAccount>> getUserByUsername(String username);

    /**
     * 更新用户信息
     *
     * @param id 用户ID
     * @param userAccount 更新的用户信息
     * @return 更新后的用户信息
     */
    Uni<ApiResponse<UserAccount>> updateUser(Long id, UserAccount userAccount);

    /**
     * 删除用户
     *
     * @param id 用户ID
     * @return 删除结果
     */
    Uni<ApiResponse<Void>> deleteUser(Long id);

    /**
     * 分页查询用户列表
     *
     * @param pageRequest 分页请求参数
     * @param keyword 搜索关键词（可选）
     * @param status 用户状态（可选）
     * @return 分页用户列表
     */
    Uni<ApiResponse<PageResult<UserAccount>>> getUserList(PageRequest pageRequest, String keyword, Integer status);

    /**
     * 激活用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> activateUser(Long id);

    /**
     * 锁定用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> lockUser(Long id);

    /**
     * 禁用用户
     *
     * @param id 用户ID
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> disableUser(Long id);

    /**
     * 重置用户密码
     *
     * @param id 用户ID
     * @param newPassword 新密码
     * @return 操作结果
     */
    Uni<ApiResponse<Void>> resetPassword(Long id, String newPassword);

    /**
     * 验证用户密码
     *
     * @param username 用户名
     * @param password 密码
     * @return 验证结果
     */
    Uni<ApiResponse<Boolean>> validatePassword(String username, String password);

    // ==================== 内部方法（用于 Resource 层调用） ====================

    /**
     * 根据条件分页查询用户列表
     * 支持多租户数据隔离
     *
     * @param tenantId 租户ID
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    Uni<PageResult<UserAccount>> findPageByConditions(Long tenantId, String keyword, Integer status, PageRequest pageRequest);

    /**
     * 根据ID和租户ID查询用户
     *
     * @param id 用户ID
     * @param tenantId 租户ID
     * @return 用户信息
     */
    Uni<UserAccount> findByIdAndTenantId(Long id, Long tenantId);

    /**
     * 根据用户名和租户ID查询用户
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @return 用户信息
     */
    Uni<UserAccount> findByUsername(Long tenantId, String username);

    /**
     * 根据邮箱和租户ID查询用户
     *
     * @param tenantId 租户ID
     * @param email 邮箱
     * @return 用户信息
     */
    Uni<UserAccount> findByEmail(Long tenantId, String email);

    /**
     * 创建用户（内部方法）
     *
     * @param userAccount 用户信息
     * @return 创建的用户信息
     */
    Uni<UserAccount> createUserInternal(UserAccount userAccount);

    /**
     * 更新用户信息（内部方法）
     *
     * @param userAccount 用户信息
     * @return 更新后的用户信息
     */
    Uni<UserAccount> updateUser(UserAccount userAccount);

    /**
     * 删除用户（内部方法）
     *
     * @param id 用户ID
     * @param tenantId 租户ID
     * @return 删除结果
     */
    Uni<Boolean> deleteUser(Long id, Long tenantId);

    /**
     * 检查用户名是否可用
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param excludeId 排除的用户ID（用于更新时检查）
     * @return 是否可用
     */
    Uni<Boolean> isUsernameAvailable(Long tenantId, String username, Long excludeId);
}
