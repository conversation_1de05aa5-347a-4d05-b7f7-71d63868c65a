package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

/**
 * 用户注册响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "用户注册响应")
public class RegisterResponse {

    @Schema(description = "用户ID", example = "1")
    private Long userId;

    @Schema(description = "用户名", example = "testuser001")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "昵称", example = "测试用户001")
    private String nickname;

    @Schema(description = "响应消息", example = "用户注册成功，请激活账户")
    private String message;
}
