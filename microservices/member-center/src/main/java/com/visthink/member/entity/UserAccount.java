package com.visthink.member.entity;

import jakarta.persistence.*;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户账户实体
 *
 * 管理系统用户的基本信息、认证信息和状态
 * 支持多种登录方式和用户状态管理
 *
 * <AUTHOR>
 */
@Entity
@Table(name = "user_accounts",
       uniqueConstraints = {
           @UniqueConstraint(columnNames = {"tenant_id", "username"}),
           @UniqueConstraint(columnNames = {"tenant_id", "email"}),
           @UniqueConstraint(columnNames = {"tenant_id", "phone_number"})
       },
       indexes = {
           @Index(name = "idx_user_tenant_username", columnList = "tenant_id, username"),
           @Index(name = "idx_user_tenant_email", columnList = "tenant_id, email"),
           @Index(name = "idx_user_tenant_phone", columnList = "tenant_id, phone_number"),
           @Index(name = "idx_user_last_login", columnList = "last_login_time")
       })
@Data
public class UserAccount {

    /**
     * 主键ID
     */
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    public Long id;

    /**
     * 租户ID
     */
    @Column(name = "tenant_id", nullable = false)
    public Long tenantId;

    /**
     * 创建时间
     */
    @Column(name = "create_time", nullable = false)
    public LocalDateTime createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time", nullable = false)
    public LocalDateTime updateTime;

    /**
     * 创建者ID
     */
    @Column(name = "creator_id")
    public Long creatorId;

    /**
     * 更新者ID
     */
    @Column(name = "updater_id")
    public Long updaterId;

    /**
     * 是否删除：0-未删除，1-已删除
     */
    @Column(name = "deleted", nullable = false)
    public Integer deleted = 0;

    /**
     * 用户名（租户内唯一）
     */
    @Column(name = "username", nullable = false, length = 50)
    private String username;

    /**
     * 邮箱地址（租户内唯一）
     */
    @Column(name = "email", nullable = false, length = 100)
    private String email;

    /**
     * 手机号码（租户内唯一）
     */
    @Column(name = "phone_number", length = 20)
    private String phoneNumber;

    /**
     * 密码哈希值
     */
    @Column(name = "password_hash", nullable = false, length = 255)
    private String passwordHash;

    /**
     * 密码盐值
     */
    @Column(name = "password_salt", length = 32)
    private String passwordSalt;

    /**
     * 真实姓名
     */
    @Column(name = "real_name", length = 50)
    private String realName;

    /**
     * 昵称
     */
    @Column(name = "nickname", length = 50)
    private String nickname;

    /**
     * 头像URL
     */
    @Column(name = "avatar_url", length = 500)
    private String avatarUrl;

    /**
     * 性别：1-男，2-女，0-未知
     */
    @Column(name = "gender")
    private Integer gender = 0;

    /**
     * 生日
     */
    @Column(name = "birthday")
    private LocalDateTime birthday;

    /**
     * 用户类型：1-普通用户，2-管理员，3-超级管理员
     */
    @Column(name = "user_type", nullable = false)
    private Integer userType = 1;

    /**
     * 账户状态：1-正常，2-锁定，3-禁用，4-待激活
     */
    @Column(name = "account_status", nullable = false)
    private Integer accountStatus = 4;

    /**
     * 是否已激活
     */
    @Column(name = "is_activated", nullable = false)
    private Boolean isActivated = false;

    /**
     * 是否已验证邮箱
     */
    @Column(name = "email_verified", nullable = false)
    private Boolean emailVerified = false;

    /**
     * 是否已验证手机
     */
    @Column(name = "phone_verified", nullable = false)
    private Boolean phoneVerified = false;

    /**
     * 最后登录时间
     */
    @Column(name = "last_login_time")
    private LocalDateTime lastLoginTime;

    /**
     * 最后登录IP
     */
    @Column(name = "last_login_ip", length = 45)
    private String lastLoginIp;

    /**
     * 登录次数
     */
    @Column(name = "login_count")
    private Long loginCount = 0L;

    /**
     * 密码错误次数
     */
    @Column(name = "password_error_count")
    private Integer passwordErrorCount = 0;

    /**
     * 账户锁定时间
     */
    @Column(name = "locked_time")
    private LocalDateTime lockedTime;

    /**
     * 密码过期时间
     */
    @Column(name = "password_expire_time")
    private LocalDateTime passwordExpireTime;

    /**
     * 账户过期时间
     */
    @Column(name = "account_expire_time")
    private LocalDateTime accountExpireTime;

    /**
     * 部门ID
     */
    @Column(name = "department_id")
    private Long departmentId;

    /**
     * 职位
     */
    @Column(name = "position", length = 100)
    private String position;

    /**
     * 工号
     */
    @Column(name = "employee_no", length = 50)
    private String employeeNo;

    /**
     * 入职时间
     */
    @Column(name = "join_time")
    private LocalDateTime joinTime;

    /**
     * 用户偏好设置（JSON格式）
     */
    @Column(name = "preferences", columnDefinition = "TEXT")
    private String preferences;

    /**
     * 扩展属性（JSON格式）
     */
    @Column(name = "extra_attributes", columnDefinition = "TEXT")
    private String extraAttributes;

    // ==================== 常量定义 ====================

    /**
     * 账户状态常量
     */
    public static class Status {
        /** 正常状态 */
        public static final Integer ACTIVE = 1;
        /** 锁定状态 */
        public static final Integer LOCKED = 2;
        /** 禁用状态 */
        public static final Integer DISABLED = 3;
        /** 待激活状态 */
        public static final Integer PENDING_ACTIVATION = 4;
    }

    /**
     * 用户类型常量
     */
    public static class UserType {
        /** 普通用户 */
        public static final Integer NORMAL = 1;
        /** 管理员 */
        public static final Integer ADMIN = 2;
        /** 超级管理员 */
        public static final Integer SUPER_ADMIN = 3;
    }

    /**
     * 性别常量
     */
    public static class Gender {
        /** 未知 */
        public static final Integer UNKNOWN = 0;
        /** 男性 */
        public static final Integer MALE = 1;
        /** 女性 */
        public static final Integer FEMALE = 2;
    }

    // ==================== 业务方法 ====================

    /**
     * 激活账户
     */
    public void activate() {
        this.isActivated = true;
        this.accountStatus = Status.ACTIVE; // 正常状态
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 锁定账户
     */
    public void lock() {
        this.accountStatus = Status.LOCKED; // 锁定状态
        this.lockedTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 解锁账户
     */
    public void unlock() {
        this.accountStatus = Status.ACTIVE; // 正常状态
        this.lockedTime = null;
        this.passwordErrorCount = 0;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 禁用账户
     */
    public void ban() {
        this.accountStatus = Status.DISABLED; // 禁用状态
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 记录登录信息
     */
    public void recordLogin(String loginIp) {
        this.lastLoginTime = LocalDateTime.now();
        this.lastLoginIp = loginIp;
        this.loginCount = this.loginCount + 1;
        this.passwordErrorCount = 0; // 重置密码错误次数
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 记录密码错误
     */
    public void recordPasswordError() {
        this.passwordErrorCount = this.passwordErrorCount + 1;
        this.updateTime = LocalDateTime.now();

        // 如果密码错误次数超过5次，锁定账户
        if (this.passwordErrorCount >= 5) {
            lock();
        }
    }

    /**
     * 验证邮箱
     */
    public void verifyEmail() {
        this.emailVerified = true;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 验证手机
     */
    public void verifyPhone() {
        this.phoneVerified = true;
        this.updateTime = LocalDateTime.now();
    }

    /**
     * 判断账户是否正常
     */
    public boolean isNormal() {
        return this.accountStatus != null && this.accountStatus.equals(Status.ACTIVE);
    }

    /**
     * 判断账户是否被锁定
     */
    public boolean isLocked() {
        return this.accountStatus != null && this.accountStatus.equals(Status.LOCKED);
    }

    /**
     * 判断账户是否被禁用
     */
    public boolean isBanned() {
        return this.accountStatus != null && this.accountStatus.equals(Status.DISABLED);
    }

    /**
     * 判断账户是否待激活
     */
    public boolean isPendingActivation() {
        return this.accountStatus != null && this.accountStatus.equals(Status.PENDING_ACTIVATION);
    }

    /**
     * 判断是否为管理员
     */
    public boolean isAdmin() {
        return this.userType != null && this.userType >= UserType.ADMIN;
    }

    /**
     * 判断是否为超级管理员
     */
    public boolean isSuperAdmin() {
        return this.userType != null && this.userType.equals(UserType.SUPER_ADMIN);
    }

    /**
     * 获取显示名称
     * 优先级：真实姓名 > 昵称 > 用户名
     */
    public String getDisplayName() {
        if (this.realName != null && !this.realName.trim().isEmpty()) {
            return this.realName;
        }
        if (this.nickname != null && !this.nickname.trim().isEmpty()) {
            return this.nickname;
        }
        return this.username;
    }

    /**
     * 获取业务主键
     */
    public String getBusinessKey() {
        return this.username;
    }
}
