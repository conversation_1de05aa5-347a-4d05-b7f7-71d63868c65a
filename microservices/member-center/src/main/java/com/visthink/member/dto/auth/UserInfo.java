package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;
import java.time.LocalDateTime;

/**
 * 用户信息DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "用户信息")
public class UserInfo {

    @Schema(description = "用户ID", example = "1")
    private Long id;

    @Schema(description = "用户名", example = "testuser001")
    private String username;

    @Schema(description = "邮箱", example = "<EMAIL>")
    private String email;

    @Schema(description = "昵称", example = "测试用户001")
    private String nickname;

    @Schema(description = "用户类型", example = "1")
    private Integer userType;

    @Schema(description = "账户状态", example = "1")
    private Integer accountStatus;

    @Schema(description = "头像URL")
    private String avatarUrl;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginTime;
}
