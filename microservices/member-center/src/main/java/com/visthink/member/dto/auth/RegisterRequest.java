package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

/**
 * 用户注册请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "用户注册请求")
public class RegisterRequest {

    @Schema(description = "用户名", required = true, example = "testuser001")
    private String username;

    @Schema(description = "密码", required = true, example = "Test123456")
    private String password;

    @Schema(description = "确认密码", required = true, example = "Test123456")
    private String confirmPassword;

    @Schema(description = "邮箱", required = true, example = "<EMAIL>")
    private String email;

    @Schema(description = "手机号", example = "13800138001")
    private String phoneNumber;

    @Schema(description = "昵称", example = "测试用户001")
    private String nickname;

    @Schema(description = "验证码", example = "TEST123")
    private String captcha;

    @Schema(description = "验证码ID", example = "test-captcha-id")
    private String captchaId;

    @Schema(description = "是否同意条款", example = "true")
    private Boolean agreeTerms;

    @Schema(description = "注册来源", example = "WEB")
    private String source;

    @Schema(description = "设备信息")
    private String deviceInfo;

    @Schema(description = "IP地址", example = "127.0.0.1")
    private String ipAddress;
}
