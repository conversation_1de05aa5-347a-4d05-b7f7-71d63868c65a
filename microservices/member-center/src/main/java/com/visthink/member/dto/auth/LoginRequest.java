package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

/**
 * 用户登录请求DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "用户登录请求")
public class LoginRequest {

    @Schema(description = "用户名", required = true, example = "testuser001")
    private String username;

    @Schema(description = "密码", required = true, example = "Test123456")
    private String password;

    @Schema(description = "记住我", example = "false")
    private Boolean rememberMe = false;

    @Schema(description = "设备信息")
    private String deviceInfo;

    @Schema(description = "IP地址", example = "127.0.0.1")
    private String ipAddress;
}
