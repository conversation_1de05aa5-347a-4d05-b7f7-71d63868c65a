package com.visthink.member.repository;

import com.visthink.member.entity.UserAccount;
import com.visthink.common.dto.PageRequest;
import com.visthink.common.dto.PageResult;
import io.smallrye.mutiny.Uni;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.hibernate.reactive.mutiny.Mutiny;

import java.time.LocalDateTime;
import java.util.List;
import org.jboss.logging.Logger;

/**
 * 用户数据访问层
 * 使用纯 Hibernate Reactive 方式实现数据库操作
 * 支持多租户数据隔离和响应式编程
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@ApplicationScoped
public class UserRepository {

    private static final Logger LOG = Logger.getLogger(UserRepository.class);

    @Inject
    Mutiny.SessionFactory sessionFactory;

    /**
     * 保存用户（新增或更新）
     *
     * @param userAccount 用户对象
     * @return 保存后的用户对象
     */
    public Uni<UserAccount> persist(UserAccount userAccount) {
        // 对于事务操作，使用用户对象中的租户ID
        Long tenantId = userAccount.tenantId;
        if (tenantId == null) {
            return Uni.createFrom().failure(new IllegalArgumentException("用户对象必须包含租户ID"));
        }

        return sessionFactory.withTransaction((session, transaction) -> {
            if (userAccount.id == null) {
                // 新增用户
                userAccount.createTime = LocalDateTime.now();
                userAccount.updateTime = LocalDateTime.now();
                return session.persist(userAccount)
                    .onItem().transform(ignored -> userAccount);
            } else {
                // 更新用户
                userAccount.updateTime = LocalDateTime.now();
                return session.merge(userAccount);
            }
        });
    }

    /**
     * 删除用户（软删除）
     *
     * @param userAccount 用户对象
     * @return 删除结果
     */
    public Uni<Boolean> delete(UserAccount userAccount) {
        // 对于事务操作，使用用户对象中的租户ID
        Long tenantId = userAccount.tenantId;
        if (tenantId == null) {
            return Uni.createFrom().failure(new IllegalArgumentException("用户对象必须包含租户ID"));
        }

        return sessionFactory.withTransaction((session, transaction) -> {
            userAccount.deleted = true;
            userAccount.updateTime = LocalDateTime.now();
            return session.merge(userAccount)
                .onItem().transform(ignored -> true);
        });
    }

    /**
     * 根据用户名和租户ID查询用户
     *
     * @param username 用户名
     * @param tenantId 租户ID
     * @return 用户对象
     */
    public Uni<UserAccount> findByUsernameAndTenantId(String username, Long tenantId) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE username = :username AND tenantId = :tenantId AND deleted = :deleted", UserAccount.class)
                .setParameter("username", username)
                .setParameter("tenantId", tenantId)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 根据ID和租户ID查询用户
     *
     * @param id 用户ID
     * @param tenantId 租户ID
     * @return 用户对象
     */
    public Uni<UserAccount> findByIdAndTenantId(Long id, Long tenantId) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE id = :id AND tenantId = :tenantId AND deleted = :deleted", UserAccount.class)
                .setParameter("id", id)
                .setParameter("tenantId", tenantId)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 根据用户名查询用户
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @return 用户对象
     */
    public Uni<UserAccount> findByUsername(Long tenantId, String username) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND username = :username AND deleted = :deleted", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("username", username)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 根据邮箱查询用户
     *
     * @param tenantId 租户ID
     * @param email 邮箱
     * @return 用户对象
     */
    public Uni<UserAccount> findByEmail(Long tenantId, String email) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND email = :email AND deleted = :deleted", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("email", email)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 根据手机号查询用户
     *
     * @param tenantId 租户ID
     * @param phoneNumber 手机号
     * @return 用户对象
     */
    public Uni<UserAccount> findByPhoneNumber(Long tenantId, String phoneNumber) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND phoneNumber = :phoneNumber AND deleted = :deleted", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("phoneNumber", phoneNumber)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 根据用户名或邮箱查询用户（用于登录）
     *
     * @param tenantId 租户ID
     * @param usernameOrEmail 用户名或邮箱
     * @return 用户对象
     */
    public Uni<UserAccount> findByUsernameOrEmail(Long tenantId, String usernameOrEmail) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND (username = :usernameOrEmail OR email = :usernameOrEmail) AND deleted = :deleted", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("usernameOrEmail", usernameOrEmail)
                .setParameter("deleted", false)
                .getSingleResultOrNull()
        );
    }

    /**
     * 检查用户名是否存在
     *
     * @param tenantId 租户ID
     * @param username 用户名
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByUsername(Long tenantId, String username, Long excludeId) {
        return sessionFactory.withSession(session -> {
            String query = excludeId != null
                ? "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND username = :username AND id != :excludeId AND deleted = false"
                : "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND username = :username AND deleted = false";

            var queryObj = session.createQuery(query, Long.class)
                .setParameter("tenantId", tenantId)
                .setParameter("username", username);

            if (excludeId != null) {
                queryObj.setParameter("excludeId", excludeId);
            }

            return queryObj.getSingleResult().map(count -> count > 0);
        });
    }

    /**
     * 检查邮箱是否存在
     *
     * @param tenantId 租户ID
     * @param email 邮箱
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByEmail(Long tenantId, String email, Long excludeId) {
        return sessionFactory.withSession(session -> {
            String query = excludeId != null
                ? "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND email = :email AND id != :excludeId AND deleted = :deleted"
                : "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND email = :email AND deleted = :deleted";

            var queryObj = session.createQuery(query, Long.class)
                .setParameter("tenantId", tenantId)
                .setParameter("email", email)
                .setParameter("deleted", false);

            if (excludeId != null) {
                queryObj.setParameter("excludeId", excludeId);
            }

            return queryObj.getSingleResult().map(count -> count > 0);
        });
    }

    /**
     * 检查手机号是否存在
     *
     * @param tenantId 租户ID
     * @param phoneNumber 手机号
     * @param excludeId 排除的用户ID
     * @return 是否存在
     */
    public Uni<Boolean> existsByPhoneNumber(Long tenantId, String phoneNumber, Long excludeId) {
        return sessionFactory.withSession(session -> {
            String query = excludeId != null
                ? "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND phoneNumber = :phoneNumber AND id != :excludeId AND deleted = :deleted"
                : "SELECT COUNT(*) FROM UserAccount WHERE tenantId = :tenantId AND phoneNumber = :phoneNumber AND deleted = :deleted";

            var queryObj = session.createQuery(query, Long.class)
                .setParameter("tenantId", tenantId)
                .setParameter("phoneNumber", phoneNumber)
                .setParameter("deleted", false);

            if (excludeId != null) {
                queryObj.setParameter("excludeId", excludeId);
            }

            return queryObj.getSingleResult().map(count -> count > 0);
        });
    }

    /**
     * 根据用户类型查询用户列表
     *
     * @param tenantId 租户ID
     * @param userType 用户类型
     * @return 用户列表
     */
    public Uni<List<UserAccount>> findByUserType(Long tenantId, Integer userType) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND userType = :userType AND deleted = :deleted ORDER BY id DESC", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("userType", userType)
                .setParameter("deleted", false)
                .getResultList()
        );
    }

    /**
     * 根据账户状态查询用户列表
     *
     * @param tenantId 租户ID
     * @param accountStatus 账户状态
     * @return 用户列表
     */
    public Uni<List<UserAccount>> findByAccountStatus(Long tenantId, Integer accountStatus) {
        return sessionFactory.withSession(session ->
            session.createQuery("FROM UserAccount WHERE tenantId = :tenantId AND accountStatus = :accountStatus AND deleted = :deleted ORDER BY id DESC", UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("accountStatus", accountStatus)
                .setParameter("deleted", false)
                .getResultList()
        );
    }

    /**
     * 根据条件分页查询用户列表
     * 使用纯 Hibernate Reactive 方式实现分页查询
     *
     * @param tenantId 租户ID
     * @param keyword 搜索关键词
     * @param status 用户状态
     * @param pageRequest 分页请求
     * @return 分页结果
     */
    public Uni<PageResult<UserAccount>> findPageByConditions(Long tenantId, String keyword, Integer status, PageRequest pageRequest) {
        return sessionFactory.withSession(session -> {
            // 构建查询条件
            StringBuilder whereClause = new StringBuilder("WHERE tenantId = :tenantId AND deleted = :deleted");

            if (keyword != null && !keyword.trim().isEmpty()) {
                whereClause.append(" AND (username LIKE :keyword OR realName LIKE :keyword OR email LIKE :keyword OR phoneNumber LIKE :keyword)");
            }

            if (status != null) {
                whereClause.append(" AND accountStatus = :status");
            }

            // 查询总数
            String countQuery = "SELECT COUNT(*) FROM UserAccount " + whereClause;
            var countQueryObj = session.createQuery(countQuery, Long.class)
                .setParameter("tenantId", tenantId)
                .setParameter("deleted", false);

            if (keyword != null && !keyword.trim().isEmpty()) {
                countQueryObj.setParameter("keyword", "%" + keyword.trim() + "%");
            }
            if (status != null) {
                countQueryObj.setParameter("status", status);
            }

            Uni<Long> totalUni = countQueryObj.getSingleResult();

            // 查询数据
            String dataQuery = "FROM UserAccount " + whereClause + " ORDER BY id DESC";
            int pageIndex = pageRequest.getPage() - 1; // 转换为从0开始的页码
            var dataQueryObj = session.createQuery(dataQuery, UserAccount.class)
                .setParameter("tenantId", tenantId)
                .setParameter("deleted", false)
                .setFirstResult(pageIndex * pageRequest.getSize())
                .setMaxResults(pageRequest.getSize());

            if (keyword != null && !keyword.trim().isEmpty()) {
                dataQueryObj.setParameter("keyword", "%" + keyword.trim() + "%");
            }
            if (status != null) {
                dataQueryObj.setParameter("status", status);
            }

            Uni<List<UserAccount>> dataUni = dataQueryObj.getResultList();

            // 组合结果
            return Uni.combine().all().unis(totalUni, dataUni)
                .asTuple()
                .onItem().transform(tuple -> {
                    Long total = tuple.getItem1();
                    List<UserAccount> records = tuple.getItem2();

                    PageResult<UserAccount> pageResult = new PageResult<>();
                    pageResult.setRecords(records);
                    pageResult.setTotal(total);
                    pageResult.setPage(pageRequest.getPage());
                    pageResult.setSize(pageRequest.getSize());
                    pageResult.setPages((int) Math.ceil((double) total / pageRequest.getSize()));

                    return pageResult;
                });
        });
    }
}
