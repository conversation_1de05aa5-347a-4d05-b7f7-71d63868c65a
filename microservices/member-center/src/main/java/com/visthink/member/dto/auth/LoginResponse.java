package com.visthink.member.dto.auth;

import lombok.Data;
import org.eclipse.microprofile.openapi.annotations.media.Schema;

/**
 * 用户登录响应DTO
 * 
 * <AUTHOR>
 */
@Data
@Schema(description = "用户登录响应")
public class LoginResponse {

    @Schema(description = "访问令牌", example = "token_1_abc123def456")
    private String accessToken;

    @Schema(description = "令牌类型", example = "Bearer")
    private String tokenType = "Bearer";

    @Schema(description = "过期时间（秒）", example = "86400")
    private Long expiresIn;

    @Schema(description = "刷新令牌")
    private String refreshToken;

    @Schema(description = "用户信息")
    private UserInfo userInfo;
}
