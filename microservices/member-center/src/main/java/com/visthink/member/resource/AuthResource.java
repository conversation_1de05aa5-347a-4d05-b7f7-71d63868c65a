package com.visthink.member.resource;

import com.visthink.common.dto.ApiResponse;
import com.visthink.common.context.TenantContext;
import com.visthink.member.dto.auth.*;
import com.visthink.member.entity.UserAccount;
import com.visthink.member.service.UserService;
import io.quarkus.logging.Log;
import io.smallrye.mutiny.Uni;
import org.eclipse.microprofile.openapi.annotations.Operation;
import org.eclipse.microprofile.openapi.annotations.Parameter;
import org.eclipse.microprofile.openapi.annotations.tags.Tag;

import jakarta.inject.Inject;
import jakarta.ws.rs.*;
import jakarta.ws.rs.core.MediaType;
import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 认证资源类
 * 
 * 提供用户认证相关的API端点，包括：
 * - 用户注册
 * - 用户登录
 * - 令牌验证
 * - 用户登出
 * - 用户名/邮箱可用性检查
 * 
 * <AUTHOR>
 */
@Path("/api/auth")
@Tag(name = "认证管理", description = "用户认证相关API")
@Produces(MediaType.APPLICATION_JSON)
@Consumes(MediaType.APPLICATION_JSON)
public class AuthResource {

    @Inject
    UserService userService;

    @Inject
    TenantContext tenantContext;

    // ==================== 用户注册 ====================

    /**
     * 用户注册
     */
    @POST
    @Path("/register")
    @Operation(summary = "用户注册", description = "创建新的用户账户")
    public Uni<ApiResponse<RegisterResponse>> register(RegisterRequest request) {
        Log.infof("用户注册请求: username=%s, email=%s", request.getUsername(), request.getEmail());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 验证注册请求
        ApiResponse<Void> validation = validateRegisterRequest(request);
        if (!validation.isSuccess()) {
            return Uni.createFrom().item(ApiResponse.error(validation.getMessage()));
        }

        // 创建用户账户
        UserAccount userAccount = new UserAccount();
        userAccount.tenantId = tenantId;
        userAccount.username = request.getUsername();
        userAccount.setPasswordHash(request.getPassword()); // 实际应该加密
        userAccount.email = request.getEmail();
        userAccount.phoneNumber = request.getPhoneNumber();
        userAccount.nickname = request.getNickname();
        userAccount.userType = 1; // 普通用户
        userAccount.accountStatus = 4; // 待激活
        userAccount.isActivated = false;
        userAccount.emailVerified = false;
        userAccount.phoneVerified = false;
        userAccount.createTime = LocalDateTime.now();
        userAccount.updateTime = LocalDateTime.now();

        return userService.createUserInternal(userAccount)
            .onItem().transform(user -> {
                RegisterResponse response = new RegisterResponse();
                response.setUserId(user.id);
                response.setUsername(user.username);
                response.setEmail(user.email);
                response.setNickname(user.nickname);
                response.setMessage("用户注册成功，请激活账户");
                return ApiResponse.success(response);
            })
            .onFailure().recoverWithItem(throwable -> {
                Log.errorf("用户注册失败: %s", throwable.getMessage());
                return ApiResponse.error("用户注册失败: " + throwable.getMessage());
            });
    }

    /**
     * 检查用户名是否可用
     */
    @GET
    @Path("/check/username")
    @Operation(summary = "检查用户名", description = "检查用户名是否可用")
    public Uni<ApiResponse<Boolean>> checkUsername(
            @Parameter(description = "用户名") @QueryParam("username") String username) {
        Log.infof("检查用户名可用性: username=%s", username);
        
        if (username == null || username.trim().isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("用户名不能为空"));
        }

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.findByUsername(tenantId, username)
            .onItem().transform(user -> {
                boolean available = (user == null);
                return ApiResponse.success(available);
            })
            .onFailure().recoverWithItem(throwable -> {
                Log.errorf("检查用户名失败: %s", throwable.getMessage());
                return ApiResponse.error("检查用户名失败: " + throwable.getMessage());
            });
    }

    /**
     * 检查邮箱是否可用
     */
    @GET
    @Path("/check/email")
    @Operation(summary = "检查邮箱", description = "检查邮箱是否可用")
    public Uni<ApiResponse<Boolean>> checkEmail(
            @Parameter(description = "邮箱") @QueryParam("email") String email) {
        Log.infof("检查邮箱可用性: email=%s", email);
        
        if (email == null || email.trim().isEmpty()) {
            return Uni.createFrom().item(ApiResponse.error("邮箱不能为空"));
        }

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.findByEmail(tenantId, email)
            .onItem().transform(user -> {
                boolean available = (user == null);
                return ApiResponse.success(available);
            })
            .onFailure().recoverWithItem(throwable -> {
                Log.errorf("检查邮箱失败: %s", throwable.getMessage());
                return ApiResponse.error("检查邮箱失败: " + throwable.getMessage());
            });
    }

    // ==================== 用户登录 ====================

    /**
     * 用户登录
     */
    @POST
    @Path("/login")
    @Operation(summary = "用户登录", description = "用户账户登录验证")
    public Uni<ApiResponse<LoginResponse>> login(LoginRequest request) {
        Log.infof("用户登录请求: username=%s", request.getUsername());
        
        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        // 验证登录请求
        if (request.getUsername() == null || request.getPassword() == null) {
            return Uni.createFrom().item(ApiResponse.error("用户名和密码不能为空"));
        }

        return userService.findByUsername(tenantId, request.getUsername())
            .onItem().transform(user -> {
                if (user == null) {
                    return ApiResponse.<LoginResponse>error("用户不存在");
                }

                // 简单密码验证（实际应该使用加密验证）
                if (!user.getPasswordHash().equals(request.getPassword())) {
                    return ApiResponse.<LoginResponse>error("密码错误");
                }

                // 检查账户状态
                if (user.accountStatus != 1) {
                    return ApiResponse.<LoginResponse>error("账户状态异常，无法登录");
                }

                // 生成访问令牌（简化实现）
                String accessToken = generateAccessToken(user);
                
                // 更新登录信息
                user.recordLogin(request.getIpAddress());

                // 构建登录响应
                LoginResponse response = new LoginResponse();
                response.setAccessToken(accessToken);
                response.setTokenType("Bearer");
                response.setExpiresIn(86400L); // 24小时
                
                UserInfo userInfo = new UserInfo();
                userInfo.setId(user.id);
                userInfo.setUsername(user.username);
                userInfo.setEmail(user.email);
                userInfo.setNickname(user.nickname);
                userInfo.setUserType(user.userType);
                response.setUserInfo(userInfo);

                return ApiResponse.success(response);
            })
            .onFailure().recoverWithItem(throwable -> {
                Log.errorf("用户登录失败: %s", throwable.getMessage());
                return ApiResponse.error("用户登录失败: " + throwable.getMessage());
            });
    }

    /**
     * 令牌验证
     */
    @GET
    @Path("/validate")
    @Operation(summary = "令牌验证", description = "验证访问令牌的有效性")
    public Uni<ApiResponse<TokenValidationResponse>> validateToken(
            @HeaderParam("Authorization") String authorization) {
        Log.info("令牌验证请求");
        
        if (authorization == null || !authorization.startsWith("Bearer ")) {
            return Uni.createFrom().item(ApiResponse.error("无效的授权头"));
        }

        String token = authorization.substring(7);
        
        // 简化的令牌验证（实际应该使用JWT验证）
        if (token.length() < 10) {
            return Uni.createFrom().item(ApiResponse.error("无效的访问令牌"));
        }

        TokenValidationResponse response = new TokenValidationResponse();
        response.setValid(true);
        response.setUserId(1L); // 简化实现
        response.setUsername("testuser");
        response.setExpiresAt(LocalDateTime.now().plusDays(1));

        return Uni.createFrom().item(ApiResponse.success(response));
    }

    /**
     * 获取当前用户信息
     */
    @GET
    @Path("/me")
    @Operation(summary = "获取当前用户", description = "获取当前登录用户的信息")
    public Uni<ApiResponse<UserInfo>> getCurrentUser(
            @Parameter(description = "用户ID") @QueryParam("userId") Long userId) {
        Log.infof("获取当前用户信息: userId=%s", userId);
        
        if (userId == null) {
            return Uni.createFrom().item(ApiResponse.error("用户ID不能为空"));
        }

        Long tenantId = tenantContext.getCurrentTenantIdSync();
        if (tenantId == null) {
            return Uni.createFrom().item(ApiResponse.error("租户信息缺失"));
        }

        return userService.findByIdAndTenantId(userId, tenantId)
            .onItem().transform(user -> {
                if (user == null) {
                    return ApiResponse.<UserInfo>error("用户不存在");
                }

                UserInfo userInfo = new UserInfo();
                userInfo.setId(user.id);
                userInfo.setUsername(user.username);
                userInfo.setEmail(user.email);
                userInfo.setNickname(user.nickname);
                userInfo.setUserType(user.userType);
                userInfo.setAccountStatus(user.accountStatus);
                userInfo.setLastLoginTime(user.lastLoginTime);

                return ApiResponse.success(userInfo);
            })
            .onFailure().recoverWithItem(throwable -> {
                Log.errorf("获取用户信息失败: %s", throwable.getMessage());
                return ApiResponse.error("获取用户信息失败: " + throwable.getMessage());
            });
    }

    /**
     * 用户登出
     */
    @POST
    @Path("/logout")
    @Operation(summary = "用户登出", description = "用户账户登出")
    public Uni<ApiResponse<Void>> logout(
            @Parameter(description = "用户ID") @QueryParam("userId") Long userId) {
        Log.infof("用户登出请求: userId=%s", userId);
        
        if (userId == null) {
            return Uni.createFrom().item(ApiResponse.error("用户ID不能为空"));
        }

        // 简化的登出实现（实际应该清理会话和令牌）
        return Uni.createFrom().item(ApiResponse.<Void>success("用户登出成功"));
    }

    // ==================== 私有辅助方法 ====================

    /**
     * 验证注册请求
     */
    private ApiResponse<Void> validateRegisterRequest(RegisterRequest request) {
        if (request.getUsername() == null || request.getUsername().trim().isEmpty()) {
            return ApiResponse.error("用户名不能为空");
        }
        if (request.getPassword() == null || request.getPassword().length() < 6) {
            return ApiResponse.error("密码长度不能少于6位");
        }
        if (request.getEmail() == null || !request.getEmail().contains("@")) {
            return ApiResponse.error("邮箱格式不正确");
        }
        if (!request.getPassword().equals(request.getConfirmPassword())) {
            return ApiResponse.error("两次输入的密码不一致");
        }
        return ApiResponse.success();
    }

    /**
     * 生成访问令牌（简化实现）
     */
    private String generateAccessToken(UserAccount user) {
        return "token_" + user.id + "_" + UUID.randomUUID().toString().replace("-", "");
    }
}
