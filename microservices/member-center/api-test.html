<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Member Center API 测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { border-left: 4px solid #28a745; }
        .error { border-left: 4px solid #dc3545; }
        .warning { border-left: 4px solid #ffc107; }
        input, select {
            width: 200px;
            padding: 8px;
            margin: 5px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Member Center API 测试工具</h1>
        <p>测试环境: <strong>http://localhost:8081</strong></p>

        <!-- 健康检查 -->
        <div class="test-section">
            <h3>1. 健康检查</h3>
            <button onclick="testHealth()">测试健康检查</button>
            <div id="health-result" class="result"></div>
        </div>

        <!-- 用户名检查 -->
        <div class="test-section">
            <h3>2. 用户名可用性检查</h3>
            <input type="text" id="username-input" placeholder="输入用户名" value="testuser">
            <button onclick="testUsernameCheck()">检查用户名</button>
            <div id="username-result" class="result"></div>
        </div>

        <!-- 邮箱检查 -->
        <div class="test-section">
            <h3>3. 邮箱可用性检查</h3>
            <input type="email" id="email-input" placeholder="输入邮箱" value="<EMAIL>">
            <button onclick="testEmailCheck()">检查邮箱</button>
            <div id="email-result" class="result"></div>
        </div>

        <!-- 用户注册 -->
        <div class="test-section">
            <h3>4. 用户注册</h3>
            <input type="text" id="reg-username" placeholder="用户名" value="testuser001">
            <input type="email" id="reg-email" placeholder="邮箱" value="<EMAIL>">
            <input type="password" id="reg-password" placeholder="密码" value="password123">
            <input type="text" id="reg-phone" placeholder="手机号" value="13800138000">
            <button onclick="testRegister()">注册用户</button>
            <div id="register-result" class="result"></div>
        </div>

        <!-- 用户登录 -->
        <div class="test-section">
            <h3>5. 用户登录</h3>
            <input type="text" id="login-username" placeholder="用户名" value="testuser001">
            <input type="password" id="login-password" placeholder="密码" value="password123">
            <button onclick="testLogin()">用户登录</button>
            <div id="login-result" class="result"></div>
        </div>

        <!-- 令牌验证 -->
        <div class="test-section">
            <h3>6. 令牌验证</h3>
            <button onclick="testTokenValidation()">验证令牌</button>
            <div id="token-result" class="result"></div>
        </div>

        <!-- 获取当前用户 -->
        <div class="test-section">
            <h3>7. 获取当前用户信息</h3>
            <button onclick="testGetCurrentUser()">获取用户信息</button>
            <div id="current-user-result" class="result"></div>
        </div>

        <!-- 用户登出 -->
        <div class="test-section">
            <h3>8. 用户登出</h3>
            <button onclick="testLogout()">用户登出</button>
            <div id="logout-result" class="result"></div>
        </div>

        <!-- 完整流程测试 -->
        <div class="test-section">
            <h3>9. 完整流程测试</h3>
            <button onclick="testFullWorkflow()">执行完整测试流程</button>
            <div id="workflow-result" class="result"></div>
        </div>
    </div>

    <script>
        const API_BASE_URL = 'http://localhost:8081';
        let currentToken = '';
        let currentUserId = null;

        // 通用请求函数
        async function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-Id': '1'
                }
            };

            if (currentToken) {
                defaultOptions.headers['Authorization'] = `Bearer ${currentToken}`;
            }

            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            const startTime = Date.now();
            try {
                const response = await fetch(url, finalOptions);
                const responseTime = Date.now() - startTime;
                const data = await response.json();
                
                return {
                    success: response.ok,
                    status: response.status,
                    data: data,
                    responseTime: responseTime
                };
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    responseTime: Date.now() - startTime
                };
            }
        }

        // 显示结果
        function showResult(elementId, result) {
            const element = document.getElementById(elementId);
            let className = result.success ? 'success' : 'error';
            
            let output = `时间: ${new Date().toLocaleTimeString()}\n`;
            output += `响应时间: ${result.responseTime}ms\n`;
            output += `状态: ${result.status || 'ERROR'}\n`;
            
            if (result.success) {
                output += `响应数据:\n${JSON.stringify(result.data, null, 2)}`;
            } else {
                output += `错误信息: ${result.error || '请求失败'}`;
            }
            
            element.textContent = output;
            element.className = `result ${className}`;
        }

        // 1. 健康检查
        async function testHealth() {
            const result = await makeRequest(`${API_BASE_URL}/health`);
            showResult('health-result', result);
        }

        // 2. 用户名检查
        async function testUsernameCheck() {
            const username = document.getElementById('username-input').value;
            const result = await makeRequest(`${API_BASE_URL}/api/auth/check/username?username=${encodeURIComponent(username)}`);
            showResult('username-result', result);
        }

        // 3. 邮箱检查
        async function testEmailCheck() {
            const email = document.getElementById('email-input').value;
            const result = await makeRequest(`${API_BASE_URL}/api/auth/check/email?email=${encodeURIComponent(email)}`);
            showResult('email-result', result);
        }

        // 4. 用户注册
        async function testRegister() {
            const registerData = {
                username: document.getElementById('reg-username').value,
                email: document.getElementById('reg-email').value,
                password: document.getElementById('reg-password').value,
                phone: document.getElementById('reg-phone').value
            };

            const result = await makeRequest(`${API_BASE_URL}/api/auth/register`, {
                method: 'POST',
                body: JSON.stringify(registerData)
            });
            
            showResult('register-result', result);
        }

        // 5. 用户登录
        async function testLogin() {
            const loginData = {
                username: document.getElementById('login-username').value,
                password: document.getElementById('login-password').value
            };

            const result = await makeRequest(`${API_BASE_URL}/api/auth/login`, {
                method: 'POST',
                body: JSON.stringify(loginData)
            });
            
            if (result.success && result.data.data) {
                currentToken = result.data.data.accessToken;
                currentUserId = result.data.data.userInfo.id;
            }
            
            showResult('login-result', result);
        }

        // 6. 令牌验证
        async function testTokenValidation() {
            const result = await makeRequest(`${API_BASE_URL}/api/auth/validate`);
            showResult('token-result', result);
        }

        // 7. 获取当前用户
        async function testGetCurrentUser() {
            const result = await makeRequest(`${API_BASE_URL}/api/auth/me`);
            showResult('current-user-result', result);
        }

        // 8. 用户登出
        async function testLogout() {
            const result = await makeRequest(`${API_BASE_URL}/api/auth/logout`, {
                method: 'POST'
            });
            
            if (result.success) {
                currentToken = '';
                currentUserId = null;
            }
            
            showResult('logout-result', result);
        }

        // 9. 完整流程测试
        async function testFullWorkflow() {
            const workflowElement = document.getElementById('workflow-result');
            workflowElement.textContent = '开始执行完整测试流程...\n';
            workflowElement.className = 'result';

            let log = '';
            
            try {
                // 步骤1: 健康检查
                log += '步骤1: 健康检查\n';
                const healthResult = await makeRequest(`${API_BASE_URL}/health`);
                log += `  - 响应时间: ${healthResult.responseTime}ms\n`;
                log += `  - 状态: ${healthResult.success ? '成功' : '失败'}\n\n`;

                // 步骤2: 检查用户名可用性
                log += '步骤2: 检查用户名可用性\n';
                const usernameResult = await makeRequest(`${API_BASE_URL}/api/auth/check/username?username=testuser002`);
                log += `  - 响应时间: ${usernameResult.responseTime}ms\n`;
                log += `  - 状态: ${usernameResult.success ? '成功' : '失败'}\n\n`;

                // 步骤3: 检查邮箱可用性
                log += '步骤3: 检查邮箱可用性\n';
                const emailResult = await makeRequest(`${API_BASE_URL}/api/auth/check/email?email=<EMAIL>`);
                log += `  - 响应时间: ${emailResult.responseTime}ms\n`;
                log += `  - 状态: ${emailResult.success ? '成功' : '失败'}\n\n`;

                // 步骤4: 用户注册
                log += '步骤4: 用户注册\n';
                const registerResult = await makeRequest(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'testuser002',
                        email: '<EMAIL>',
                        password: 'password123',
                        phone: '13800138002'
                    })
                });
                log += `  - 响应时间: ${registerResult.responseTime}ms\n`;
                log += `  - 状态: ${registerResult.success ? '成功' : '失败'}\n\n`;

                // 步骤5: 用户登录
                log += '步骤5: 用户登录\n';
                const loginResult = await makeRequest(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'testuser002',
                        password: 'password123'
                    })
                });
                log += `  - 响应时间: ${loginResult.responseTime}ms\n`;
                log += `  - 状态: ${loginResult.success ? '成功' : '失败'}\n`;
                
                if (loginResult.success && loginResult.data.data) {
                    currentToken = loginResult.data.data.accessToken;
                    currentUserId = loginResult.data.data.userInfo.id;
                    log += `  - 获取到Token: ${currentToken ? '是' : '否'}\n\n`;
                }

                // 步骤6: 令牌验证
                log += '步骤6: 令牌验证\n';
                const tokenResult = await makeRequest(`${API_BASE_URL}/api/auth/validate`);
                log += `  - 响应时间: ${tokenResult.responseTime}ms\n`;
                log += `  - 状态: ${tokenResult.success ? '成功' : '失败'}\n\n`;

                // 步骤7: 获取当前用户信息
                log += '步骤7: 获取当前用户信息\n';
                const currentUserResult = await makeRequest(`${API_BASE_URL}/api/auth/me`);
                log += `  - 响应时间: ${currentUserResult.responseTime}ms\n`;
                log += `  - 状态: ${currentUserResult.success ? '成功' : '失败'}\n\n`;

                // 步骤8: 用户登出
                log += '步骤8: 用户登出\n';
                const logoutResult = await makeRequest(`${API_BASE_URL}/api/auth/logout`, {
                    method: 'POST'
                });
                log += `  - 响应时间: ${logoutResult.responseTime}ms\n`;
                log += `  - 状态: ${logoutResult.success ? '成功' : '失败'}\n\n`;

                log += '=== 测试完成 ===\n';
                log += `总体状态: 所有API端点已测试完成\n`;
                
                workflowElement.textContent = log;
                workflowElement.className = 'result success';
                
            } catch (error) {
                log += `\n错误: ${error.message}\n`;
                workflowElement.textContent = log;
                workflowElement.className = 'result error';
            }
        }

        // 页面加载完成后自动执行健康检查
        window.onload = function() {
            testHealth();
        };
    </script>
</body>
</html>
