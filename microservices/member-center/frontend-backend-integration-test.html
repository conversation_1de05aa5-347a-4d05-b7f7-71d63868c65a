<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前后端接口联调测试 - 用户管理和权限系统</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1, h2, h3 {
            color: #333;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
            background: #fafafa;
        }
        .form-group {
            margin: 10px 0;
        }
        label {
            display: inline-block;
            width: 120px;
            font-weight: bold;
        }
        input, textarea, select {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .success {
            background: #28a745;
        }
        .danger {
            background: #dc3545;
        }
        .warning {
            background: #ffc107;
            color: #333;
        }
        .result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
            background: #e9ecef;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        .api-info {
            background: #e7f3ff;
            padding: 10px;
            border-left: 4px solid #007bff;
            margin: 10px 0;
        }
        .step-indicator {
            display: flex;
            justify-content: space-between;
            margin: 20px 0;
        }
        .step {
            flex: 1;
            text-align: center;
            padding: 10px;
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            position: relative;
        }
        .step.active {
            background: #007bff;
            color: white;
        }
        .step.completed {
            background: #28a745;
            color: white;
        }
        .step.error {
            background: #dc3545;
            color: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 前后端接口联调测试 - 用户管理和权限系统</h1>

        <div class="api-info">
            <h3>📋 测试环境信息</h3>
            <p><strong>后端服务:</strong> member-center (http://localhost:8081)</p>
            <p><strong>前端框架:</strong> Vue Vben Admin 5.57</p>
            <p><strong>测试目标:</strong> 验证用户登录注册、权限菜单授权系统的核心功能</p>
        </div>

        <!-- 测试步骤指示器 -->
        <div class="step-indicator">
            <div class="step" id="step1">1. 服务健康检查</div>
            <div class="step" id="step2">2. 用户注册</div>
            <div class="step" id="step3">3. 用户登录</div>
            <div class="step" id="step4">4. 权限验证</div>
            <div class="step" id="step5">5. 菜单授权</div>
        </div>

        <!-- 第一步：服务健康检查 -->
        <div class="test-section">
            <h2>🏥 第一步：服务健康检查</h2>
            <div class="form-group">
                <button onclick="checkServiceHealth()" class="success">检查服务状态</button>
                <button onclick="checkApiEndpoints()" class="warning">检查API端点</button>
            </div>
            <div id="healthResult" class="result"></div>
        </div>

        <!-- 第二步：用户注册测试 -->
        <div class="test-section">
            <h2>📝 第二步：用户注册测试</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="regUsername" value="testuser001" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="regPassword" value="Test123456" placeholder="输入密码">
            </div>
            <div class="form-group">
                <label>确认密码:</label>
                <input type="password" id="regConfirmPassword" value="Test123456" placeholder="确认密码">
            </div>
            <div class="form-group">
                <label>邮箱:</label>
                <input type="email" id="regEmail" value="<EMAIL>" placeholder="输入邮箱">
            </div>
            <div class="form-group">
                <label>手机号:</label>
                <input type="text" id="regPhone" value="13800138001" placeholder="输入手机号">
            </div>
            <div class="form-group">
                <label>昵称:</label>
                <input type="text" id="regNickname" value="测试用户001" placeholder="输入昵称">
            </div>
            <div class="form-group">
                <button onclick="checkUsernameAvailable()" class="warning">检查用户名</button>
                <button onclick="checkEmailAvailable()" class="warning">检查邮箱</button>
                <button onclick="registerUser()" class="success">注册用户</button>
            </div>
            <div id="registerResult" class="result"></div>
        </div>

        <!-- 第三步：用户登录测试 -->
        <div class="test-section">
            <h2>🔑 第三步：用户登录测试</h2>
            <div class="form-group">
                <label>用户名:</label>
                <input type="text" id="loginUsername" value="testuser001" placeholder="输入用户名">
            </div>
            <div class="form-group">
                <label>密码:</label>
                <input type="password" id="loginPassword" value="Test123456" placeholder="输入密码">
            </div>
            <div class="form-group">
                <label>记住我:</label>
                <input type="checkbox" id="rememberMe" style="width: auto;">
            </div>
            <div class="form-group">
                <button onclick="loginUser()" class="success">用户登录</button>
                <button onclick="getCurrentUser()" class="warning">获取用户信息</button>
                <button onclick="validateToken()" class="warning">验证令牌</button>
                <button onclick="logoutUser()" class="danger">用户登出</button>
            </div>
            <div id="loginResult" class="result"></div>
        </div>

        <!-- 第四步：权限验证测试 -->
        <div class="test-section">
            <h2>🛡️ 第四步：权限验证测试</h2>
            <div class="form-group">
                <label>用户ID:</label>
                <input type="number" id="permUserId" value="1" placeholder="输入用户ID">
            </div>
            <div class="form-group">
                <label>角色代码:</label>
                <input type="text" id="roleCode" value="ADMIN" placeholder="输入角色代码">
            </div>
            <div class="form-group">
                <label>权限代码:</label>
                <input type="text" id="permissionCode" value="USER_MANAGE" placeholder="输入权限代码">
            </div>
            <div class="form-group">
                <button onclick="getUserRoles()" class="warning">获取用户角色</button>
                <button onclick="getUserPermissions()" class="warning">获取用户权限</button>
                <button onclick="assignUserRole()" class="success">分配角色</button>
                <button onclick="revokeUserRole()" class="danger">撤销角色</button>
            </div>
            <div id="permissionResult" class="result"></div>
        </div>

        <!-- 第五步：菜单授权测试 -->
        <div class="test-section">
            <h2>📋 第五步：菜单授权测试</h2>
            <div class="form-group">
                <label>菜单代码:</label>
                <input type="text" id="menuCode" value="USER_MANAGEMENT" placeholder="输入菜单代码">
            </div>
            <div class="form-group">
                <label>菜单名称:</label>
                <input type="text" id="menuName" value="用户管理" placeholder="输入菜单名称">
            </div>
            <div class="form-group">
                <button onclick="getUserMenus()" class="warning">获取用户菜单</button>
                <button onclick="checkMenuPermission()" class="warning">检查菜单权限</button>
                <button onclick="assignMenuPermission()" class="success">分配菜单权限</button>
                <button onclick="revokeMenuPermission()" class="danger">撤销菜单权限</button>
            </div>
            <div id="menuResult" class="result"></div>
        </div>

        <!-- 多租户测试 -->
        <div class="test-section">
            <h2>🏢 多租户数据隔离测试</h2>
            <div class="form-group">
                <label>租户ID:</label>
                <input type="number" id="tenantId" value="1" placeholder="输入租户ID">
            </div>
            <div class="form-group">
                <button onclick="testTenantIsolation()" class="warning">测试数据隔离</button>
                <button onclick="switchTenant()" class="warning">切换租户</button>
            </div>
            <div id="tenantResult" class="result"></div>
        </div>

        <!-- 性能测试 -->
        <div class="test-section">
            <h2>⚡ API性能测试</h2>
            <div class="form-group">
                <label>并发数:</label>
                <input type="number" id="concurrentCount" value="5" placeholder="并发请求数">
            </div>
            <div class="form-group">
                <button onclick="performanceTest()" class="warning">性能测试</button>
                <button onclick="stressTest()" class="danger">压力测试</button>
            </div>
            <div id="performanceResult" class="result"></div>
        </div>

        <!-- 测试总结 -->
        <div class="test-section">
            <h2>📊 测试总结报告</h2>
            <div id="summaryResult" class="result"></div>
            <div class="form-group">
                <button onclick="generateReport()" class="success">生成测试报告</button>
                <button onclick="exportResults()" class="warning">导出结果</button>
                <button onclick="clearResults()" class="danger">清空结果</button>
            </div>
        </div>
    </div>

    <script>
        // 全局配置
        const API_BASE_URL = 'http://localhost:8081';
        const DEFAULT_TENANT_ID = 1;
        let currentAccessToken = '';
        let currentUserId = null;

        // 测试状态
        let testStatus = {
            authResourceMissing: false,
            availableEndpoints: []
        };
        let testResults = {
            health: null,
            register: null,
            login: null,
            permissions: null,
            menus: null,
            performance: null
        };

        // 工具函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleString();
            const prefix = `[${timestamp}] `;
            console.log(prefix + message);
            return prefix + message;
        }

        function updateStepStatus(stepId, status) {
            const step = document.getElementById(stepId);
            if (step) {
                step.className = 'step ' + status;
            }
        }

        function displayResult(elementId, content, isError = false) {
            const element = document.getElementById(elementId);
            if (element) {
                element.textContent = content;
                element.style.color = isError ? '#dc3545' : '#333';
            }
        }

        function makeRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'X-Tenant-Id': DEFAULT_TENANT_ID.toString()
                }
            };

            if (currentAccessToken) {
                defaultOptions.headers['Authorization'] = `Bearer ${currentAccessToken}`;
            }

            const finalOptions = {
                ...defaultOptions,
                ...options,
                headers: {
                    ...defaultOptions.headers,
                    ...options.headers
                }
            };

            return fetch(url, finalOptions);
        }

        // 第一步：服务健康检查
        async function checkServiceHealth() {
            updateStepStatus('step1', 'active');
            const startTime = Date.now();

            try {
                log('开始检查服务健康状态...');

                // 检查健康端点
                const healthResponse = await makeRequest(`${API_BASE_URL}/health`);
                const healthTime = Date.now() - startTime;

                let result = `=== 服务健康检查结果 ===\n`;
                result += `健康检查状态: ${healthResponse.status}\n`;
                result += `响应时间: ${healthTime}ms\n`;

                if (healthResponse.ok) {
                    const healthData = await healthResponse.text();
                    result += `健康状态: ${healthData}\n`;
                }

                // 检查OpenAPI文档
                try {
                    const openApiResponse = await makeRequest(`${API_BASE_URL}/openapi`);
                    result += `OpenAPI文档: ${openApiResponse.status === 200 ? '可用' : '不可用'}\n`;
                } catch (e) {
                    result += `OpenAPI文档: 不可用\n`;
                }

                // 检查Swagger UI
                try {
                    const swaggerResponse = await makeRequest(`${API_BASE_URL}/swagger-ui`);
                    result += `Swagger UI: ${swaggerResponse.status === 200 ? '可用' : '不可用'}\n`;
                } catch (e) {
                    result += `Swagger UI: 不可用\n`;
                }

                result += `\n✅ 服务健康检查完成`;
                displayResult('healthResult', result);
                updateStepStatus('step1', 'completed');
                testResults.health = { success: true, responseTime: healthTime };

            } catch (error) {
                const errorMsg = `❌ 服务健康检查失败: ${error.message}`;
                displayResult('healthResult', errorMsg, true);
                updateStepStatus('step1', 'error');
                testResults.health = { success: false, error: error.message };
            }
        }

        async function checkApiEndpoints() {
            try {
                log('检查API端点...');

                const endpoints = [
                    { path: '/api/auth/check/username', method: 'GET', name: '用户名检查' },
                    { path: '/api/auth/check/email', method: 'GET', name: '邮箱检查' },
                    { path: '/api/users', method: 'GET', name: '用户列表' },
                    { path: '/api/roles', method: 'GET', name: '角色列表' },
                    { path: '/api/permissions', method: 'GET', name: '权限列表' },
                    { path: '/api/menus', method: 'GET', name: '菜单列表' }
                ];

                let result = `=== API端点检查结果 ===\n`;

                for (const endpoint of endpoints) {
                    try {
                        const response = await makeRequest(`${API_BASE_URL}${endpoint.path}?test=1`);
                        const status = response.status < 500 ? '✅' : '❌';
                        result += `${status} ${endpoint.name}: ${response.status}\n`;
                    } catch (e) {
                        result += `❌ ${endpoint.name}: 连接失败\n`;
                    }
                }

                displayResult('healthResult', result);

            } catch (error) {
                displayResult('healthResult', `❌ API端点检查失败: ${error.message}`, true);
            }
        }

        // 第二步：用户注册测试
        async function checkUsernameAvailable() {
            try {
                const username = document.getElementById('regUsername').value;
                if (!username) {
                    alert('请输入用户名');
                    return;
                }

                log(`检查用户名可用性: ${username}`);

                const response = await makeRequest(`${API_BASE_URL}/api/auth/check/username?username=${encodeURIComponent(username)}`);
                const data = await response.json();

                let result = `=== 用户名检查结果 ===\n`;
                result += `用户名: ${username}\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应: ${JSON.stringify(data, null, 2)}\n`;

                if (data.success && data.data) {
                    result += `✅ 用户名可用`;
                } else {
                    result += `❌ 用户名不可用或检查失败`;
                }

                displayResult('registerResult', result);

            } catch (error) {
                displayResult('registerResult', `❌ 用户名检查失败: ${error.message}`, true);
            }
        }

        async function checkEmailAvailable() {
            try {
                const email = document.getElementById('regEmail').value;
                if (!email) {
                    alert('请输入邮箱');
                    return;
                }

                log(`检查邮箱可用性: ${email}`);

                const response = await makeRequest(`${API_BASE_URL}/api/auth/check/email?email=${encodeURIComponent(email)}`);
                const data = await response.json();

                let result = `=== 邮箱检查结果 ===\n`;
                result += `邮箱: ${email}\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应: ${JSON.stringify(data, null, 2)}\n`;

                if (data.success && data.data) {
                    result += `✅ 邮箱可用`;
                } else {
                    result += `❌ 邮箱不可用或检查失败`;
                }

                displayResult('registerResult', result);

            } catch (error) {
                displayResult('registerResult', `❌ 邮箱检查失败: ${error.message}`, true);
            }
        }

        async function registerUser() {
            updateStepStatus('step2', 'active');
            const startTime = Date.now();

            try {
                const registerData = {
                    username: document.getElementById('regUsername').value,
                    password: document.getElementById('regPassword').value,
                    confirmPassword: document.getElementById('regConfirmPassword').value,
                    email: document.getElementById('regEmail').value,
                    phoneNumber: document.getElementById('regPhone').value,
                    nickname: document.getElementById('regNickname').value,
                    captcha: 'TEST123',
                    captchaId: 'test-captcha-id',
                    agreeTerms: true,
                    source: 'WEB',
                    deviceInfo: navigator.userAgent,
                    ipAddress: '127.0.0.1'
                };

                log(`用户注册请求: ${registerData.username}`);

                const response = await makeRequest(`${API_BASE_URL}/api/auth/register`, {
                    method: 'POST',
                    body: JSON.stringify(registerData)
                });

                const responseTime = Date.now() - startTime;
                const data = await response.json();

                let result = `=== 用户注册结果 ===\n`;
                result += `用户名: ${registerData.username}\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应时间: ${responseTime}ms\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 用户注册成功`;
                    updateStepStatus('step2', 'completed');
                    testResults.register = { success: true, responseTime, data };

                    // 自动填充登录信息
                    document.getElementById('loginUsername').value = registerData.username;
                    document.getElementById('loginPassword').value = registerData.password;
                } else {
                    result += `❌ 用户注册失败: ${data.message || '未知错误'}`;
                    updateStepStatus('step2', 'error');
                    testResults.register = { success: false, error: data.message };
                }

                displayResult('registerResult', result);

            } catch (error) {
                const errorMsg = `❌ 用户注册失败: ${error.message}`;
                displayResult('registerResult', errorMsg, true);
                updateStepStatus('step2', 'error');
                testResults.register = { success: false, error: error.message };
            }
        }

        // 第三步：用户登录测试
        async function loginUser() {
            updateStepStatus('step3', 'active');
            const startTime = Date.now();

            try {
                const loginData = {
                    username: document.getElementById('loginUsername').value,
                    password: document.getElementById('loginPassword').value,
                    rememberMe: document.getElementById('rememberMe').checked,
                    deviceInfo: navigator.userAgent,
                    ipAddress: '127.0.0.1'
                };

                log(`用户登录请求: ${loginData.username}`);

                const response = await makeRequest(`${API_BASE_URL}/api/auth/login`, {
                    method: 'POST',
                    body: JSON.stringify(loginData)
                });

                const responseTime = Date.now() - startTime;
                const data = await response.json();

                let result = `=== 用户登录结果 ===\n`;
                result += `用户名: ${loginData.username}\n`;
                result += `状态码: ${response.status}\n`;
                result += `响应时间: ${responseTime}ms\n`;
                result += `响应数据: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success && data.data) {
                    currentAccessToken = data.data.accessToken;
                    currentUserId = data.data.userInfo?.id;

                    result += `✅ 用户登录成功\n`;
                    result += `访问令牌: ${currentAccessToken.substring(0, 20)}...\n`;
                    result += `用户ID: ${currentUserId}\n`;
                    result += `令牌过期时间: ${data.data.expiresIn}秒\n`;

                    updateStepStatus('step3', 'completed');
                    testResults.login = { success: true, responseTime, data };

                    // 自动填充用户ID到权限测试
                    if (currentUserId) {
                        document.getElementById('permUserId').value = currentUserId;
                    }
                } else {
                    result += `❌ 用户登录失败: ${data.message || '未知错误'}`;
                    updateStepStatus('step3', 'error');
                    testResults.login = { success: false, error: data.message };
                }

                displayResult('loginResult', result);

            } catch (error) {
                const errorMsg = `❌ 用户登录失败: ${error.message}`;
                displayResult('loginResult', errorMsg, true);
                updateStepStatus('step3', 'error');
                testResults.login = { success: false, error: error.message };
            }
        }

        async function getCurrentUser() {
            try {
                if (!currentUserId) {
                    alert('请先登录获取用户ID');
                    return;
                }

                log(`获取当前用户信息: ${currentUserId}`);

                const response = await makeRequest(`${API_BASE_URL}/api/auth/me?userId=${currentUserId}`);
                const data = await response.json();

                let result = `=== 当前用户信息 ===\n`;
                result += `状态码: ${response.status}\n`;
                result += `用户信息: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 获取用户信息成功`;
                } else {
                    result += `❌ 获取用户信息失败: ${data.message || '未知错误'}`;
                }

                displayResult('loginResult', result);

            } catch (error) {
                displayResult('loginResult', `❌ 获取用户信息失败: ${error.message}`, true);
            }
        }

        async function validateToken() {
            try {
                if (!currentAccessToken) {
                    alert('请先登录获取访问令牌');
                    return;
                }

                log('验证访问令牌...');

                const response = await makeRequest(`${API_BASE_URL}/api/auth/validate`);
                const data = await response.json();

                let result = `=== 令牌验证结果 ===\n`;
                result += `状态码: ${response.status}\n`;
                result += `验证结果: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success && data.data) {
                    result += `✅ 令牌验证成功`;
                } else {
                    result += `❌ 令牌验证失败`;
                }

                displayResult('loginResult', result);

            } catch (error) {
                displayResult('loginResult', `❌ 令牌验证失败: ${error.message}`, true);
            }
        }

        async function logoutUser() {
            try {
                if (!currentUserId) {
                    alert('请先登录');
                    return;
                }

                log('用户登出...');

                const response = await makeRequest(`${API_BASE_URL}/api/auth/logout?userId=${currentUserId}`, {
                    method: 'POST'
                });
                const data = await response.json();

                let result = `=== 用户登出结果 ===\n`;
                result += `状态码: ${response.status}\n`;
                result += `登出结果: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 用户登出成功`;
                    currentAccessToken = '';
                    currentUserId = null;
                } else {
                    result += `❌ 用户登出失败: ${data.message || '未知错误'}`;
                }

                displayResult('loginResult', result);

            } catch (error) {
                displayResult('loginResult', `❌ 用户登出失败: ${error.message}`, true);
            }
        }

        // 权限相关功能（简化实现）
        async function getUserRoles() {
            try {
                const userId = document.getElementById('permUserId').value;
                if (!userId) {
                    alert('请输入用户ID');
                    return;
                }

                log(`获取用户角色: ${userId}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/roles`);
                const data = await response.json();

                let result = `=== 用户角色列表 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `状态码: ${response.status}\n`;
                result += `角色数据: ${JSON.stringify(data, null, 2)}\n`;

                displayResult('permissionResult', result);

            } catch (error) {
                displayResult('permissionResult', `❌ 获取用户角色失败: ${error.message}`, true);
            }
        }

        async function getUserPermissions() {
            try {
                const userId = document.getElementById('permUserId').value;
                if (!userId) {
                    alert('请输入用户ID');
                    return;
                }

                log(`获取用户权限: ${userId}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/permissions`);
                const data = await response.json();

                let result = `=== 用户权限列表 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `状态码: ${response.status}\n`;
                result += `权限数据: ${JSON.stringify(data, null, 2)}\n`;

                displayResult('permissionResult', result);

            } catch (error) {
                displayResult('permissionResult', `❌ 获取用户权限失败: ${error.message}`, true);
            }
        }

        async function assignUserRole() {
            try {
                const userId = document.getElementById('permUserId').value;
                const roleCode = document.getElementById('roleCode').value;

                if (!userId || !roleCode) {
                    alert('请输入用户ID和角色代码');
                    return;
                }

                log(`分配用户角色: ${userId} -> ${roleCode}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/roles`, {
                    method: 'POST',
                    body: JSON.stringify({ roleCode })
                });
                const data = await response.json();

                let result = `=== 分配角色结果 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `角色代码: ${roleCode}\n`;
                result += `状态码: ${response.status}\n`;
                result += `结果: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 角色分配成功`;
                    updateStepStatus('step4', 'completed');
                } else {
                    result += `❌ 角色分配失败: ${data.message || '未知错误'}`;
                }

                displayResult('permissionResult', result);

            } catch (error) {
                displayResult('permissionResult', `❌ 分配角色失败: ${error.message}`, true);
            }
        }

        async function revokeUserRole() {
            try {
                const userId = document.getElementById('permUserId').value;
                const roleCode = document.getElementById('roleCode').value;

                if (!userId || !roleCode) {
                    alert('请输入用户ID和角色代码');
                    return;
                }

                log(`撤销用户角色: ${userId} -> ${roleCode}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/roles/${roleCode}`, {
                    method: 'DELETE'
                });
                const data = await response.json();

                let result = `=== 撤销角色结果 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `角色代码: ${roleCode}\n`;
                result += `状态码: ${response.status}\n`;
                result += `结果: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 角色撤销成功`;
                } else {
                    result += `❌ 角色撤销失败: ${data.message || '未知错误'}`;
                }

                displayResult('permissionResult', result);

            } catch (error) {
                displayResult('permissionResult', `❌ 撤销角色失败: ${error.message}`, true);
            }
        }

        // 菜单相关功能
        async function getUserMenus() {
            try {
                const userId = document.getElementById('permUserId').value || currentUserId;
                if (!userId) {
                    alert('请输入用户ID或先登录');
                    return;
                }

                log(`获取用户菜单: ${userId}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/menus`);
                const data = await response.json();

                let result = `=== 用户菜单列表 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `状态码: ${response.status}\n`;
                result += `菜单数据: ${JSON.stringify(data, null, 2)}\n`;

                if (response.ok && data.success) {
                    result += `✅ 获取用户菜单成功`;
                    updateStepStatus('step5', 'completed');
                } else {
                    result += `❌ 获取用户菜单失败: ${data.message || '未知错误'}`;
                }

                displayResult('menuResult', result);

            } catch (error) {
                displayResult('menuResult', `❌ 获取用户菜单失败: ${error.message}`, true);
            }
        }

        async function checkMenuPermission() {
            try {
                const userId = document.getElementById('permUserId').value || currentUserId;
                const menuCode = document.getElementById('menuCode').value;

                if (!userId || !menuCode) {
                    alert('请输入用户ID和菜单代码');
                    return;
                }

                log(`检查菜单权限: ${userId} -> ${menuCode}`);

                const response = await makeRequest(`${API_BASE_URL}/api/users/${userId}/menus/${menuCode}/permission`);
                const data = await response.json();

                let result = `=== 菜单权限检查结果 ===\n`;
                result += `用户ID: ${userId}\n`;
                result += `菜单代码: ${menuCode}\n`;
                result += `状态码: ${response.status}\n`;
                result += `权限结果: ${JSON.stringify(data, null, 2)}\n`;

                displayResult('menuResult', result);

            } catch (error) {
                displayResult('menuResult', `❌ 检查菜单权限失败: ${error.message}`, true);
            }
        }

        async function assignMenuPermission() {
            alert('菜单权限分配功能需要在角色管理中实现');
        }

        async function revokeMenuPermission() {
            alert('菜单权限撤销功能需要在角色管理中实现');
        }

        // 多租户测试
        async function testTenantIsolation() {
            try {
                const tenantId = document.getElementById('tenantId').value;
                log(`测试租户数据隔离: ${tenantId}`);

                // 使用不同租户ID获取用户列表
                const response1 = await makeRequest(`${API_BASE_URL}/api/users?page=1&size=5`, {
                    headers: { 'X-Tenant-Id': '1' }
                });

                const response2 = await makeRequest(`${API_BASE_URL}/api/users?page=1&size=5`, {
                    headers: { 'X-Tenant-Id': tenantId }
                });

                let result = `=== 多租户数据隔离测试 ===\n`;
                result += `租户1数据: ${response1.status}\n`;
                result += `租户${tenantId}数据: ${response2.status}\n`;

                if (response1.ok && response2.ok) {
                    const data1 = await response1.json();
                    const data2 = await response2.json();
                    result += `租户1用户数: ${data1.data?.total || 0}\n`;
                    result += `租户${tenantId}用户数: ${data2.data?.total || 0}\n`;
                    result += `✅ 多租户隔离测试完成`;
                } else {
                    result += `❌ 多租户隔离测试失败`;
                }

                displayResult('tenantResult', result);

            } catch (error) {
                displayResult('tenantResult', `❌ 多租户测试失败: ${error.message}`, true);
            }
        }

        async function switchTenant() {
            const tenantId = document.getElementById('tenantId').value;
            if (tenantId) {
                DEFAULT_TENANT_ID = parseInt(tenantId);
                alert(`已切换到租户 ${tenantId}`);
            }
        }

        // 性能测试
        async function performanceTest() {
            try {
                const concurrentCount = parseInt(document.getElementById('concurrentCount').value) || 5;
                log(`开始性能测试，并发数: ${concurrentCount}`);

                const startTime = Date.now();
                const promises = [];

                for (let i = 0; i < concurrentCount; i++) {
                    promises.push(makeRequest(`${API_BASE_URL}/health`));
                }

                const responses = await Promise.all(promises);
                const endTime = Date.now();
                const totalTime = endTime - startTime;

                let result = `=== API性能测试结果 ===\n`;
                result += `并发请求数: ${concurrentCount}\n`;
                result += `总耗时: ${totalTime}ms\n`;
                result += `平均响应时间: ${Math.round(totalTime / concurrentCount)}ms\n`;
                result += `成功请求数: ${responses.filter(r => r.ok).length}\n`;
                result += `失败请求数: ${responses.filter(r => !r.ok).length}\n`;

                if (totalTime < 500 * concurrentCount) {
                    result += `✅ 性能测试通过`;
                } else {
                    result += `❌ 性能测试未达标`;
                }

                displayResult('performanceResult', result);
                testResults.performance = {
                    success: true,
                    totalTime,
                    avgTime: Math.round(totalTime / concurrentCount),
                    concurrentCount
                };

            } catch (error) {
                displayResult('performanceResult', `❌ 性能测试失败: ${error.message}`, true);
            }
        }

        async function stressTest() {
            alert('压力测试功能需要更复杂的实现，建议使用专业的压力测试工具');
        }

        // 测试报告生成
        function generateReport() {
            let report = `=== 前后端接口联调测试报告 ===\n`;
            report += `测试时间: ${new Date().toLocaleString()}\n`;
            report += `测试环境: ${API_BASE_URL}\n\n`;

            // 健康检查结果
            if (testResults.health) {
                report += `1. 服务健康检查: ${testResults.health.success ? '✅ 通过' : '❌ 失败'}\n`;
                if (testResults.health.responseTime) {
                    report += `   响应时间: ${testResults.health.responseTime}ms\n`;
                }
            }

            // 用户注册结果
            if (testResults.register) {
                report += `2. 用户注册: ${testResults.register.success ? '✅ 通过' : '❌ 失败'}\n`;
                if (testResults.register.responseTime) {
                    report += `   响应时间: ${testResults.register.responseTime}ms\n`;
                }
            }

            // 用户登录结果
            if (testResults.login) {
                report += `3. 用户登录: ${testResults.login.success ? '✅ 通过' : '❌ 失败'}\n`;
                if (testResults.login.responseTime) {
                    report += `   响应时间: ${testResults.login.responseTime}ms\n`;
                }
            }

            // 性能测试结果
            if (testResults.performance) {
                report += `4. 性能测试: ✅ 完成\n`;
                report += `   并发数: ${testResults.performance.concurrentCount}\n`;
                report += `   平均响应时间: ${testResults.performance.avgTime}ms\n`;
            }

            report += `\n=== 测试总结 ===\n`;
            const totalTests = Object.values(testResults).filter(r => r !== null).length;
            const passedTests = Object.values(testResults).filter(r => r && r.success).length;
            report += `总测试项: ${totalTests}\n`;
            report += `通过测试: ${passedTests}\n`;
            report += `测试通过率: ${totalTests > 0 ? Math.round(passedTests / totalTests * 100) : 0}%\n`;

            displayResult('summaryResult', report);
        }

        function exportResults() {
            const report = document.getElementById('summaryResult').textContent;
            const blob = new Blob([report], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `integration-test-report-${new Date().toISOString().slice(0, 19)}.txt`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function clearResults() {
            const resultElements = ['healthResult', 'registerResult', 'loginResult', 'permissionResult', 'menuResult', 'tenantResult', 'performanceResult', 'summaryResult'];
            resultElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) element.textContent = '';
            });

            // 重置步骤状态
            for (let i = 1; i <= 5; i++) {
                updateStepStatus(`step${i}`, '');
            }

            // 重置测试结果
            testResults = {
                health: null,
                register: null,
                login: null,
                permissions: null,
                menus: null,
                performance: null
            };

            // 重置令牌
            currentAccessToken = '';
            currentUserId = null;

            alert('测试结果已清空');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('前后端接口联调测试页面加载完成');

            // 自动检查服务健康状态
            setTimeout(() => {
                checkServiceHealth();
            }, 1000);
        });
    </script>
</body>
</html>
