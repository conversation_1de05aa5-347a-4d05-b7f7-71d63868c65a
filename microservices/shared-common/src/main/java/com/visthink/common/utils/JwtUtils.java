package com.visthink.common.utils;

import com.visthink.common.config.SecureConfigManager;
import io.smallrye.jwt.build.Jwt;
import jakarta.enterprise.context.ApplicationScoped;
import jakarta.inject.Inject;
import org.jboss.logging.Logger;

import java.time.Duration;
import java.time.Instant;
import java.util.Arrays;
import java.util.HashSet;
import java.util.UUID;

@ApplicationScoped
public class JwtUtils {

    private static final Logger LOG = Logger.getLogger(JwtUtils.class);

    @Inject
    SecureConfigManager secureConfigManager;

    public String generateToken(String username, Long userId) {
        return generateToken(username, userId, null);
    }

    public String generateToken(String username, Long userId, Long tenantId) {
        try {
            var jwtBuilder = Jwt.issuer(secureConfigManager.getJwtIssuer())
                    .subject(username)
                    .groups(new HashSet<>(Arrays.asList("USER")))
                    .claim("userId", userId)
                    .claim("iat", Instant.now().getEpochSecond())
                    .claim("jti", UUID.randomUUID().toString()) // 添加JWT ID防止重放攻击
                    .expiresIn(Duration.ofMillis(secureConfigManager.getJwtExpiration()));

            // 添加租户ID到JWT声明中
            if (tenantId != null) {
                jwtBuilder.claim("tenantId", tenantId);
            }

            // 使用HMAC-SHA256算法签名（与SmallRye JWT配置保持一致）
            String token = jwtBuilder.signWithSecret(secureConfigManager.getJwtSecret());
            LOG.debugf("生成JWT token成功，用户：%s，租户：%s", username, tenantId);
            return token;
        } catch (Exception e) {
            LOG.errorf(e, "生成JWT token失败，用户：%s", username);
            throw new RuntimeException("JWT token生成失败", e);
        }
    }

    public String generateRefreshToken(String username, Long userId) {
        return generateRefreshToken(username, userId, null);
    }

    public String generateRefreshToken(String username, Long userId, Long tenantId) {
        try {
            var jwtBuilder = Jwt.issuer(secureConfigManager.getJwtIssuer())
                    .subject(username)
                    .claim("userId", userId)
                    .claim("refresh", true)
                    .claim("iat", Instant.now().getEpochSecond())
                    .claim("jti", UUID.randomUUID().toString()) // 添加JWT ID防止重放攻击
                    .expiresIn(Duration.ofDays(7)); // 刷新token有效期7天

            // 添加租户ID到JWT声明中
            if (tenantId != null) {
                jwtBuilder.claim("tenantId", tenantId);
            }

            // 使用HMAC-SHA256算法签名（与SmallRye JWT配置保持一致）
            String refreshToken = jwtBuilder.signWithSecret(secureConfigManager.getJwtSecret());
            LOG.debugf("生成刷新token成功，用户：%s，租户：%s", username, tenantId);
            return refreshToken;
        } catch (Exception e) {
            LOG.errorf(e, "生成刷新token失败，用户：%s", username);
            throw new RuntimeException("刷新token生成失败", e);
        }
    }

    /**
     * 验证JWT token是否有效
     *
     * @param token JWT token
     * @return 是否有效
     */
    public boolean validateToken(String token) {
        try {
            // 这里可以添加更多验证逻辑，如检查黑名单、验证签名等
            return token != null && !token.trim().isEmpty();
        } catch (Exception e) {
            LOG.warnf(e, "JWT token验证失败：%s", token);
            return false;
        }
    }
}
